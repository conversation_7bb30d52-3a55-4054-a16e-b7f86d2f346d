"use client";

import { formatCurrency } from "@/lib/utils/format";
import React, { useState, useMemo } from "react";
import { EditProductModal } from "../product/edit-product-modal";
import { MoreVertical, Search, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import Link from "next/link";

import { useDebounce } from "@/lib/hooks";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ProductStatus } from "@prisma/client";
import { User } from "@/lib/types/user";
import Image from "next/image";
import { toast } from "sonner";

interface TableProduct {
  id: string;
  name: string;
  description: string | null;
  price: number;
  imageUrl: string | null;
  image: string | null;
  capacity: number;
  stock: number;
  status: ProductStatus;
  category: string | null;
  overtimeRate: number | null;
  userId: string;
  user?: User;
  createdAt: Date;
  updatedAt: Date;
}

interface ProductTableProps {
  products: TableProduct[];
  currentPage: number;
  totalPages: number;
  totalProducts: number;
  searchQuery: string;
}

export function ProductTable({ products, currentPage, totalPages, totalProducts, searchQuery }: ProductTableProps) {
  const [selectedProduct, setSelectedProduct] = useState<TableProduct | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [search, setSearch] = useState(searchQuery);
  const debouncedSearch = useDebounce(search, 300);

  // Use server-provided products (already paginated and filtered)
  const paginatedProducts = products;

  // Calculate pagination info
  const itemsPerPage = 5;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalProducts);

  const handleEdit = (product: TableProduct) => {
    setSelectedProduct(product);
    setIsEditModalOpen(true);
  };

  const handleUpdate = () => {
    // Refresh data
    window.location.reload();
  };

  const handleStatusChange = async (product: TableProduct) => {
    try {
      const newStatus = product.status === 'AVAILABLE' ? 'NOT_AVAILABLE' : 'AVAILABLE';
      const response = await fetch(`/api/products/${product.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || errorData.message || 'Gagal mengubah status');
      }

      toast.success('Status berhasil diubah');
      handleUpdate();
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error(error instanceof Error ? error.message : 'Gagal mengubah status');
    }
  };

  const handleDelete = async (product: TableProduct) => {
    if (!confirm('Apakah Anda yakin ingin menghapus produk ini?')) {
      return;
    }

    console.log('Starting delete for product:', product.id);

    try {
      const url = `/api/products/${product.id}`;
      console.log('DELETE request URL:', url);

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        let errorMessage = 'Gagal menghapus produk';
        let responseText = '';

        try {
          responseText = await response.text();
          console.log('Response text:', responseText);

          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.log('Parsed error data:', errorData);
        } catch (parseError) {
          console.error('Error parsing response:', parseError);
          console.log('Raw response text:', responseText);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('Delete success result:', result);
      toast.success(result.message || 'Produk berhasil dihapus');
      handleUpdate();
    } catch (error) {
      console.error('Error deleting product:', error);
      const errorMessage = error instanceof Error ? error.message : 'Gagal menghapus produk';
      toast.error(errorMessage);
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Cari produk..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        {searchQuery && (
          <div className="text-sm text-muted-foreground">
            Menampilkan {totalProducts} hasil untuk &quot;{searchQuery}&quot;
          </div>
        )}
      </div>

      <div className="overflow-x-auto scrollbar-hide bg-card rounded-lg border border-border shadow-sm">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-blue-50 dark:bg-gray-800">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Produk
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Harga
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Tarif Overtime
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Stok
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-20">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody className="bg-card divide-y divide-border">
            {paginatedProducts.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                      <Search className="w-6 h-6 text-muted-foreground" />
                    </div>
                    <div className="text-sm font-medium text-foreground">
                      Tidak ada produk ditemukan
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {searchQuery
                        ? `Tidak ada hasil untuk "${searchQuery}"`
                        : 'Belum ada produk yang ditambahkan'
                      }
                    </div>
                  </div>
                </td>
              </tr>
            ) : (
              paginatedProducts.map((product) => (
              <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {product.imageUrl && (
                      <div className="relative h-10 w-10">
                        <Image
                          src={product.imageUrl}
                          alt={product.name}
                          fill
                          sizes="(max-width: 768px) 40px"
                          className="rounded-lg object-cover"
                        />
                      </div>
                    )}
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {product.capacity} KVA
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {formatCurrency(product.price)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {formatCurrency(product.overtimeRate || 0)}
                    <div className="text-xs text-gray-500 dark:text-gray-400">per jam</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{product.stock}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    product.status === 'AVAILABLE'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100'
                      : 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100'
                  }`}>
                    {product.status === 'AVAILABLE' ? 'Tersedia' : 'Tidak Tersedia'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0 text-gray-500 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
                        <MoreVertical className="h-4 w-4 dark:text-gray-300" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="dark:bg-gray-800 dark:border-gray-700">
                      <DropdownMenuItem onClick={() => handleEdit(product)} className="dark:text-gray-200 dark:hover:bg-gray-700">
                        Edit Detail
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleStatusChange(product)} className="dark:text-gray-200 dark:hover:bg-gray-700">
                        {product.status === 'AVAILABLE' ? 'Set Tidak Tersedia' : 'Set Tersedia'}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="dark:border-gray-700" />
                      <DropdownMenuItem
                        onClick={() => handleDelete(product)}
                        className="text-red-600 focus:text-red-600 focus:bg-red-50 dark:text-red-400 dark:focus:text-red-400 dark:focus:bg-red-950 dark:hover:bg-gray-700"
                      >
                        Hapus
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Server-Side Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6 px-2">
          <div className="text-sm text-muted-foreground">
            Menampilkan {startIndex + 1}-{endIndex} dari {totalProducts} data
          </div>

          <div className="flex items-center gap-2">
            {/* Previous Button */}
            {currentPage > 1 ? (
              <Link href={`?page=${currentPage - 1}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}`}>
                <button className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-1">
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </button>
              </Link>
            ) : (
              <button disabled className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md opacity-50 cursor-not-allowed flex items-center gap-1">
                <ChevronLeft className="w-4 h-4" />
                Previous
              </button>
            )}

            {/* Page Numbers */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <Link key={page} href={`?page=${page}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}`}>
                <button
                  className={`px-3 py-2 text-sm border rounded-md min-w-[40px] ${
                    page === currentPage
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              </Link>
            ))}

            {/* Next Button */}
            {currentPage < totalPages ? (
              <Link href={`?page=${currentPage + 1}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}`}>
                <button className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-1">
                  Next
                  <ChevronRight className="w-4 h-4" />
                </button>
              </Link>
            ) : (
              <button disabled className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md opacity-50 cursor-not-allowed flex items-center gap-1">
                Next
                <ChevronRight className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      )}

      {selectedProduct && selectedProduct.user && (
        <EditProductModal
          product={selectedProduct}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onUpdate={handleUpdate}
        />
      )}
    </div>
  );
}
